"""
经典分类器检测模块
支持多种传统机器学习算法进行轴承故障分类检测
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import seaborn as sns
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QTextEdit, QScrollArea, QFrame, QSplitter,
    QGroupBox, QProgressBar, QMessageBox, QFileDialog, QCheckBox,
    QTabWidget, QSpinBox, QDoubleSpinBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QListWidget, QListWidgetItem, QSlider, QFormLayout
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QCursor

# 算法模块导入
from algorithms import create_classical_classifier

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)


class ModelTrainingThread(QThread):
    """模型训练线程"""
    progress_updated = pyqtSignal(int, str)
    training_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, X, y, classifier_type, params, test_size=0.2):
        super().__init__()
        self.X = X
        self.y = y
        self.classifier_type = classifier_type
        self.params = params
        self.test_size = test_size
        self.classifier = create_classical_classifier()
        
    def run(self):
        """执行模型训练"""
        try:
            # 进度回调函数
            def progress_callback(progress, message):
                self.progress_updated.emit(progress, message)

            # 准备数据
            self.progress_updated.emit(10, "准备数据...")
            X_train, X_test, y_train, y_test = self.classifier.prepare_data(
                self.X, self.y, test_size=self.test_size
            )

            # 训练模型
            training_result = self.classifier.train(
                X_train, y_train,
                algorithm=self.classifier_type,
                config=self.params,
                progress_callback=progress_callback
            )

            # 评估模型
            self.progress_updated.emit(90, "评估模型...")
            evaluation_result = self.classifier.evaluate(X_test, y_test)

            # 获取特征重要性
            feature_importance = self.classifier.get_feature_importance()

            # 合并结果
            results = {
                'classifier': self.classifier,
                'training_result': training_result,
                'evaluation_result': evaluation_result,
                'feature_importance': feature_importance
            }

            self.training_completed.emit(results)

        except Exception as e:
            self.error_occurred.emit(str(e))


class ClassicalClassifier(QWidget):
    """经典分类器检测界面"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.current_classifier = None
        self.feature_data = None

        # 分类器选项
        self.classifier_options = {
            "SVM": {"C": 1.0, "gamma": "scale", "kernel": "rbf"},
            "Random Forest": {"n_estimators": 100, "max_depth": None, "random_state": 42},
            "KNN": {"n_neighbors": 5, "weights": "uniform"},
            "Naive Bayes": {},
            "Decision Tree": {"max_depth": None, "random_state": 42}
        }

        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("经典分类器检测")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧控制面板
        self.create_control_panel(main_splitter)
        
        # 右侧结果显示区域
        self.create_result_panel(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([400, 800])
        
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(15)
        
        # 数据源选择
        data_group = QGroupBox("数据源选择")
        data_layout = QVBoxLayout(data_group)
        
        self.load_data_btn = QPushButton("加载特征数据")
        self.load_data_btn.clicked.connect(self.load_feature_data)
        self.load_data_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                font-size: 16px;
                padding: 10px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
        """)
        data_layout.addWidget(self.load_data_btn)
        
        self.data_status_label = QLabel("未加载数据")
        self.data_status_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 14px;")
        data_layout.addWidget(self.data_status_label)
        
        control_layout.addWidget(data_group)
        
        # 特征选择
        feature_group = QGroupBox("特征选择")
        feature_layout = QVBoxLayout(feature_group)
        
        self.feature_checkboxes = {}
        feature_types = [
            "时域有量纲特征", "时域无量纲特征", "频域特征", 
            "时频域特征", "小波特征", "EMD特征"
        ]
        
        for feature_type in feature_types:
            checkbox = QCheckBox(feature_type)
            checkbox.setChecked(True)
            self.feature_checkboxes[feature_type] = checkbox
            feature_layout.addWidget(checkbox)
        
        control_layout.addWidget(feature_group)
        
        # 分类器选择
        classifier_group = QGroupBox("分类器配置")
        classifier_layout = QFormLayout(classifier_group)
        
        self.classifier_combo = QComboBox()
        self.classifier_combo.addItems(list(self.classifier_options.keys()))
        self.classifier_combo.currentTextChanged.connect(self.update_parameters)
        classifier_layout.addRow("分类器类型:", self.classifier_combo)
        
        # 参数配置区域
        self.param_widget = QWidget()
        self.param_layout = QFormLayout(self.param_widget)
        classifier_layout.addRow(self.param_widget)
        
        control_layout.addWidget(classifier_group)
        
        # 训练配置
        training_group = QGroupBox("训练配置")
        training_layout = QFormLayout(training_group)
        
        self.test_size_slider = QSlider(Qt.Horizontal)
        self.test_size_slider.setRange(10, 50)
        self.test_size_slider.setValue(20)
        self.test_size_slider.valueChanged.connect(self.update_test_size_label)
        
        self.test_size_label = QLabel("20%")
        test_size_layout = QHBoxLayout()
        test_size_layout.addWidget(self.test_size_slider)
        test_size_layout.addWidget(self.test_size_label)
        training_layout.addRow("测试集比例:", test_size_layout)
        
        control_layout.addWidget(training_group)
        
        # 操作按钮
        button_layout = QVBoxLayout()
        
        self.train_btn = QPushButton("训练模型")
        self.train_btn.clicked.connect(self.train_model)
        self.train_btn.setEnabled(False)
        button_layout.addWidget(self.train_btn)
        
        self.save_model_btn = QPushButton("保存模型")
        self.save_model_btn.clicked.connect(self.save_model)
        self.save_model_btn.setEnabled(False)
        button_layout.addWidget(self.save_model_btn)
        
        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_model)
        button_layout.addWidget(self.load_model_btn)
        
        control_layout.addWidget(QWidget())  # 占位符
        control_layout.addLayout(button_layout)
        
        parent.addWidget(control_widget)
        
        # 初始化参数界面
        self.update_parameters()

    def create_result_panel(self, parent):
        """创建右侧结果显示面板"""
        result_widget = QWidget()
        self.result_layout = QVBoxLayout(result_widget)
        self.result_layout.setContentsMargins(10, 10, 10, 10)
        self.result_layout.setSpacing(15)

        # 训练状态
        status_group = QGroupBox("训练状态")
        status_layout = QVBoxLayout(status_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("等待开始训练...")
        self.status_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 16px;")
        status_layout.addWidget(self.status_label)

        self.result_layout.addWidget(status_group)

        # 结果显示区域（滚动）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.results_widget = QWidget()
        self.results_layout = QVBoxLayout(self.results_widget)
        scroll_area.setWidget(self.results_widget)

        self.result_layout.addWidget(scroll_area)
        parent.addWidget(result_widget)

    def update_parameters(self):
        """更新参数配置界面"""
        # 清除现有参数控件
        while self.param_layout.count():
            child = self.param_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        classifier_type = self.classifier_combo.currentText()
        params = self.classifier_options[classifier_type]

        self.param_controls = {}

        for param_name, default_value in params.items():
            if isinstance(default_value, int):
                control = QSpinBox()
                control.setRange(1, 1000)
                control.setValue(default_value)
            elif isinstance(default_value, float):
                control = QDoubleSpinBox()
                control.setRange(0.001, 100.0)
                control.setDecimals(3)
                control.setValue(default_value)
            elif isinstance(default_value, str):
                control = QComboBox()
                if param_name == "kernel":
                    control.addItems(["rbf", "linear", "poly", "sigmoid"])
                elif param_name == "gamma":
                    control.addItems(["scale", "auto"])
                elif param_name == "weights":
                    control.addItems(["uniform", "distance"])
                control.setCurrentText(default_value)
            else:
                control = QLineEdit(str(default_value))

            self.param_controls[param_name] = control
            self.param_layout.addRow(f"{param_name}:", control)

    def update_test_size_label(self):
        """更新测试集比例标签"""
        value = self.test_size_slider.value()
        self.test_size_label.setText(f"{value}%")

    def load_feature_data(self):
        """加载特征数据"""
        try:
            # 这里应该从数据库加载特征数据
            # 暂时使用示例数据
            QMessageBox.information(self, "提示", "特征数据加载功能需要连接到特征提取模块")

            # 示例数据生成（实际应用中应从数据库读取）
            np.random.seed(42)
            n_samples = 1000
            n_features = 20

            # 生成示例特征数据
            X = np.random.randn(n_samples, n_features)
            # 生成示例标签（正常、内圈故障、外圈故障、滚动体故障）
            y = np.random.choice(['正常', '内圈故障', '外圈故障', '滚动体故障'], n_samples)

            self.feature_data = pd.DataFrame(X, columns=[f'特征_{i+1}' for i in range(n_features)])
            self.feature_data['标签'] = y

            self.data_status_label.setText(f"已加载 {len(self.feature_data)} 条数据")
            self.data_status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 14px;")
            self.train_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据失败: {str(e)}")

    def get_selected_features(self):
        """获取选中的特征"""
        if self.feature_data is None:
            return None, None

        # 这里应该根据选中的特征类型筛选特征
        # 暂时返回所有特征
        X = self.feature_data.drop('标签', axis=1)
        y = self.feature_data['标签']

        return X, y

    def get_classifier_params(self):
        """获取分类器参数"""
        params = {}
        for param_name, control in self.param_controls.items():
            if isinstance(control, QSpinBox):
                params[param_name] = control.value()
            elif isinstance(control, QDoubleSpinBox):
                params[param_name] = control.value()
            elif isinstance(control, QComboBox):
                params[param_name] = control.currentText()
            elif isinstance(control, QLineEdit):
                text = control.text()
                try:
                    # 尝试转换为数字
                    if '.' in text:
                        params[param_name] = float(text)
                    else:
                        params[param_name] = int(text)
                except ValueError:
                    params[param_name] = text

        return params

    def train_model(self):
        """训练模型"""
        try:
            # 获取特征数据
            X, y = self.get_selected_features()
            if X is None:
                QMessageBox.warning(self, "警告", "请先加载特征数据")
                return

            # 数据预处理
            X_scaled = self.scaler.fit_transform(X)
            y_encoded = self.label_encoder.fit_transform(y)

            # 划分训练测试集
            test_size = self.test_size_slider.value() / 100.0
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_encoded, test_size=test_size, random_state=42, stratify=y_encoded
            )

            # 获取分类器参数
            classifier_type = self.classifier_combo.currentText()
            params = self.get_classifier_params()

            # 显示训练状态
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("开始训练...")
            self.train_btn.setEnabled(False)

            # 启动训练线程
            self.training_thread = ModelTrainingThread(
                X_train, X_test, y_train, y_test, classifier_type, params
            )
            self.training_thread.progress_updated.connect(self.update_training_progress)
            self.training_thread.training_completed.connect(self.on_training_completed)
            self.training_thread.error_occurred.connect(self.on_training_error)
            self.training_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"训练失败: {str(e)}")
            self.train_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

    def update_training_progress(self, value, message):
        """更新训练进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_training_completed(self, results):
        """训练完成处理"""
        self.current_model = results['classifier']
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练完成!")
        self.train_btn.setEnabled(True)
        self.save_model_btn.setEnabled(True)

        # 显示结果
        self.display_results(results)

    def on_training_error(self, error_message):
        """训练错误处理"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练失败")
        self.train_btn.setEnabled(True)
        QMessageBox.critical(self, "训练错误", error_message)

    def display_results(self, results):
        """显示训练结果"""
        # 清除之前的结果
        while self.results_layout.count():
            child = self.results_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 性能指标表格
        metrics_group = QGroupBox("模型性能指标")
        metrics_layout = QVBoxLayout(metrics_group)

        metrics_table = QTableWidget(4, 2)
        metrics_table.setHorizontalHeaderLabels(["指标", "数值"])
        metrics_table.horizontalHeader().setStretchLastSection(True)

        metrics_data = [
            ("准确率", f"{results['accuracy']:.4f}"),
            ("精确率", f"{results['precision']:.4f}"),
            ("召回率", f"{results['recall']:.4f}"),
            ("F1分数", f"{results['f1_score']:.4f}")
        ]

        for i, (metric, value) in enumerate(metrics_data):
            metrics_table.setItem(i, 0, QTableWidgetItem(metric))
            metrics_table.setItem(i, 1, QTableWidgetItem(value))

        metrics_table.setMaximumHeight(200)
        metrics_layout.addWidget(metrics_table)
        self.results_layout.addWidget(metrics_group)

        # 交叉验证结果
        cv_group = QGroupBox("交叉验证结果")
        cv_layout = QVBoxLayout(cv_group)

        cv_label = QLabel(f"5折交叉验证平均准确率: {results['cv_scores'].mean():.4f} (±{results['cv_scores'].std()*2:.4f})")
        cv_label.setStyleSheet(f"font-size: 16px; color: {TEXT_PRIMARY};")
        cv_layout.addWidget(cv_label)
        self.results_layout.addWidget(cv_group)

        # 混淆矩阵可视化
        self.create_confusion_matrix_plot(results['confusion_matrix'], results['y_test'], results['y_pred'])

        # 特征重要性图（如果可用）
        if results['feature_importance'] is not None:
            self.create_feature_importance_plot(results['feature_importance'])

    def create_confusion_matrix_plot(self, cm, y_test, y_pred):
        """创建混淆矩阵图"""
        cm_group = QGroupBox("混淆矩阵")
        cm_layout = QVBoxLayout(cm_group)

        # 创建matplotlib图形
        fig = Figure(figsize=(8, 6), facecolor=PRIMARY_BG)
        canvas = FigureCanvas(fig)

        ax = fig.add_subplot(111)

        # 获取类别标签
        labels = self.label_encoder.classes_

        # 绘制混淆矩阵热图
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=labels, yticklabels=labels, ax=ax)
        ax.set_title('混淆矩阵', fontsize=16, color=TEXT_PRIMARY)
        ax.set_xlabel('预测标签', fontsize=14, color=TEXT_PRIMARY)
        ax.set_ylabel('真实标签', fontsize=14, color=TEXT_PRIMARY)

        # 设置图表样式
        ax.set_facecolor(SECONDARY_BG)
        fig.patch.set_facecolor(PRIMARY_BG)

        fig.tight_layout()

        cm_layout.addWidget(canvas)
        self.results_layout.addWidget(cm_group)

    def create_feature_importance_plot(self, feature_importance):
        """创建特征重要性图"""
        fi_group = QGroupBox("特征重要性")
        fi_layout = QVBoxLayout(fi_group)

        # 创建matplotlib图形
        fig = Figure(figsize=(10, 6), facecolor=PRIMARY_BG)
        canvas = FigureCanvas(fig)

        ax = fig.add_subplot(111)

        # 获取特征名称
        feature_names = [f'特征_{i+1}' for i in range(len(feature_importance))]

        # 排序特征重要性
        indices = np.argsort(feature_importance)[::-1][:10]  # 显示前10个重要特征

        # 绘制条形图
        ax.bar(range(len(indices)), feature_importance[indices], color=ACCENT_COLOR)
        ax.set_title('特征重要性 (前10名)', fontsize=16, color=TEXT_PRIMARY)
        ax.set_xlabel('特征', fontsize=14, color=TEXT_PRIMARY)
        ax.set_ylabel('重要性', fontsize=14, color=TEXT_PRIMARY)
        ax.set_xticks(range(len(indices)))
        ax.set_xticklabels([feature_names[i] for i in indices], rotation=45)

        # 设置图表样式
        ax.set_facecolor(SECONDARY_BG)
        ax.tick_params(colors=TEXT_PRIMARY)
        fig.patch.set_facecolor(PRIMARY_BG)

        fig.tight_layout()

        fi_layout.addWidget(canvas)
        self.results_layout.addWidget(fi_group)

    def save_model(self):
        """保存训练好的模型"""
        if self.current_model is None:
            QMessageBox.warning(self, "警告", "没有可保存的模型")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存模型", "", "模型文件 (*.pkl);;所有文件 (*)"
            )

            if file_path:
                # 保存模型和预处理器
                model_data = {
                    'classifier': self.current_model,
                    'scaler': self.scaler,
                    'label_encoder': self.label_encoder,
                    'classifier_type': self.classifier_combo.currentText()
                }

                joblib.dump(model_data, file_path)
                QMessageBox.information(self, "成功", "模型保存成功!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存模型失败: {str(e)}")

    def load_model(self):
        """加载训练好的模型"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载模型", "", "模型文件 (*.pkl);;所有文件 (*)"
            )

            if file_path:
                model_data = joblib.load(file_path)

                self.current_model = model_data['classifier']
                self.scaler = model_data['scaler']
                self.label_encoder = model_data['label_encoder']

                # 更新界面
                classifier_type = model_data.get('classifier_type', 'SVM')
                self.classifier_combo.setCurrentText(classifier_type)

                self.save_model_btn.setEnabled(True)
                self.status_label.setText("模型加载成功!")

                QMessageBox.information(self, "成功", "模型加载成功!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载模型失败: {str(e)}")
